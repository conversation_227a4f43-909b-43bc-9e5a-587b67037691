package cn.iocoder.yudao.server;

import java.nio.file.*;
import java.sql.*;
import java.util.regex.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class TyphoonDataProcessor {
    private static final String DB_URL = "********************************************";
    private static final String DB_USER = "postgres";
    private static final String DB_PASSWORD = "jsgs@1985";
    private static final String DATA_PATH = "C:\\Users\\<USER>\\Desktop\\台风数据";

    public static void main(String[] args) {
        try {
            processAllFiles();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void processAllFiles() throws Exception {
        Path dataDir = Paths.get(DATA_PATH);

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dataDir)) {
            for (Path file : stream) {
                if (Files.isRegularFile(file)) {
                    System.out.println("处理文件: " + file.getFileName());
                    processFile(file);
                }
            }
        }
    }

    private static void processFile(Path filePath) throws Exception {
        String content = Files.readString(filePath);

        // 提取台风ID
        Pattern idPattern = Pattern.compile("nameless_(\\d+)");
        Matcher matcher = idPattern.matcher(filePath.getFileName().toString());
        if (!matcher.find()) {
            System.out.println("无法从文件名提取台风ID: " + filePath.getFileName());
            return;
        }

        Long typhoonId = Long.parseLong(matcher.group(1));

        // 提取JSON数据
        Pattern jsonPattern = Pattern.compile("typhoon_jsons_view_\\d+\\((.+)\\)$");
        Matcher jsonMatcher = jsonPattern.matcher(content.trim());
        if (!jsonMatcher.find()) {
            System.out.println("无法提取JSON数据: " + filePath.getFileName());
            return;
        }

        String jsonStr = jsonMatcher.group(1);
        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(jsonStr);

        insertTrackData(typhoonId, root);
    }

    private static void insertTrackData(Long typhoonId, JsonNode root) throws SQLException {
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            conn.setAutoCommit(false);

            // 先检查台风基本信息是否存在
            if (!checkTyphoonExists(conn, typhoonId)) {
                System.out.println("台风ID " + typhoonId + " 在基本信息表中不存在，跳过处理");
                return;
            }

            // 清除该台风的旧轨迹数据（如果需要重新导入）
            clearOldTrackData(conn, typhoonId);

            // 插入轨迹数据
            JsonNode typhoon = root.get("typhoon");
            JsonNode tracks = typhoon.get(8);

            if (tracks != null && tracks.isArray()) {
                int trackCount = 0;
                for (JsonNode track : tracks) {
                    insertSingleTrack(conn, typhoonId, track);
                    trackCount++;
                }
                System.out.println("台风 " + typhoonId + " 插入了 " + trackCount + " 条轨迹记录");
            }

            conn.commit();
        }
    }

    private static boolean checkTyphoonExists(Connection conn, Long typhoonId) throws SQLException {
        String sql = "SELECT COUNT(*) FROM typhoon_info WHERE id = ?";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setLong(1, typhoonId);
            ResultSet rs = stmt.executeQuery();
            rs.next();
            return rs.getInt(1) > 0;
        }
    }

    private static void clearOldTrackData(Connection conn, Long typhoonId) throws SQLException {
        String sql = "DELETE FROM typhoon_track WHERE typhoon_id = ?";
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setLong(1, typhoonId);
            int deleted = stmt.executeUpdate();
            if (deleted > 0) {
                System.out.println("清除台风 " + typhoonId + " 的 " + deleted + " 条旧轨迹数据");
            }
        }
    }

    private static void insertSingleTrack(Connection conn, Long typhoonId, JsonNode track) throws SQLException {
        String sql = "INSERT INTO typhoon_track (typhoon_id, track_id, time_str, timestamp_ms, " +
                "level, longitude, latitude, pressure, wind_speed, direction, speed) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setLong(1, typhoonId);
            stmt.setLong(2, track.get(0).asLong());
            stmt.setString(3, track.get(1).asText());
            stmt.setLong(4, track.get(2).asLong());
            stmt.setString(5, track.get(3).asText());
            stmt.setBigDecimal(6, track.get(4).decimalValue());
            stmt.setBigDecimal(7, track.get(5).decimalValue());
            stmt.setInt(8, track.get(6).asInt());
            stmt.setInt(9, track.get(7).asInt());
            stmt.setString(10, track.get(8).asText());
            stmt.setInt(11, track.get(9).asInt());
            stmt.executeUpdate();
        }
    }
}